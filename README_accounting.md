# برنامج المحاسبة المالية - سيت الحل
# Financial Accounting Program - Set Al-Hal

## 📋 وصف البرنامج / Program Description

برنامج محاسبة مالية احترافي باللغة العربية مصمم خصيصاً للشركات والمؤسسات العربية. يوفر واجهة مستخدم عصرية وسهلة الاستخدام مع دعم كامل للغة العربية ومحاذاة النصوص من اليمين إلى اليسار.

Professional Arabic financial accounting software designed specifically for Arab companies and institutions. Provides a modern and user-friendly interface with full Arabic language support and right-to-left text alignment.

## ✨ المميزات الرئيسية / Key Features

### 🎨 واجهة المستخدم / User Interface
- ✅ تصميم عصري واحترافي باستخدام CustomTkinter
- ✅ دعم كامل للغة العربية مع محاذاة RTL
- ✅ أيقونات ملونة وجذابة لكل قسم
- ✅ وضع ليلي ونهاري قابل للتبديل
- ✅ واجهة متجاوبة تتكيف مع أحجام الشاشات المختلفة

### 📊 الأقسام الرئيسية / Main Sections
- 📈 **التقارير** - تقارير مالية شاملة
- 🧾 **الفواتير** - إدارة الفواتير والمبيعات
- 💰 **الخزينة** - إدارة النقدية والخزينة
- 📋 **الحسابات** - إدارة الحسابات والعملاء
- 📦 **البضاعة** - إدارة المخزون والمنتجات
- 📊 **تحليل البيانات** - تحليلات مالية متقدمة

### 🔧 العمليات المالية / Financial Operations
- ➕ **إدخال الحسابات** - إضافة حسابات جديدة
- ⚙️ **الإعدادات** - تخصيص البرنامج
- 📊 **الجرد** - عمليات جرد المخزون
- 💸 **الصرف** - عمليات الصرف والمدفوعات
- 📈 **المؤشرات** - مؤشرات الأداء المالي
- 🔄 **المرتجعات** - إدارة مرتجعات البيع والشراء

## 🛠️ متطلبات التشغيل / System Requirements

### 📋 المكتبات المطلوبة / Required Libraries
```bash
pip install customtkinter>=5.2.0
pip install arabic-reshaper>=3.0.0
pip install python-bidi>=0.6.6
```

### 💻 متطلبات النظام / System Requirements
- **نظام التشغيل**: Windows 10/11, macOS, Linux
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM كحد أدنى
- **مساحة القرص**: 100 MB مساحة فارغة

## 🚀 طريقة التشغيل / How to Run

### 1️⃣ تثبيت المكتبات / Install Dependencies
```bash
# تثبيت المكتبات الأساسية
pip install -r requirements_additional.txt

# أو تثبيت المكتبات يدوياً
pip install customtkinter arabic-reshaper python-bidi
```

### 2️⃣ تشغيل البرنامج / Run the Program
```bash
# الطريقة الأولى - تشغيل مباشر
python accounting_main_interface.py

# الطريقة الثانية - ملف التشغيل
python run_accounting_app.py
```

## 📁 هيكل الملفات / File Structure

```
📦 برنامج المحاسبة المالية/
├── 📄 accounting_main_interface.py    # الواجهة الرئيسية
├── 📄 run_accounting_app.py          # ملف التشغيل
├── 📄 requirements_additional.txt     # المكتبات المطلوبة
├── 📄 requirements_barcode.txt       # مكتبات الباركود
└── 📄 README_accounting.md           # دليل الاستخدام
```

## 🎯 الاستخدام / Usage

### 🖱️ التنقل في الواجهة / Interface Navigation
1. **الشريط العلوي**: يعرض معلومات المستخدم والشركة
2. **القوائم الرئيسية**: ملف، تحرير، تقارير، مساعدة
3. **الأيقونات الملونة**: للوصول السريع للأقسام المختلفة
4. **الشريط السفلي**: الوقت الحالي والأزرار السريعة

### ⚡ الأزرار السريعة / Quick Buttons
- 🌙 **تبديل الوضع**: تغيير بين الوضع الليلي والنهاري
- 📞 **خدمة العملاء**: الاتصال بالدعم الفني
- 💳 **الاشتراك**: معلومات الاشتراك والترخيص
- 🏢 **الموزعين**: قائمة الموزعين المعتمدين

## 🔧 التخصيص / Customization

### 🎨 تغيير الألوان / Color Customization
يمكن تخصيص ألوان الأيقونات من خلال تعديل قاموس الألوان في الكود:

```python
# في دالة create_main_content
sections = [
    ("اسم القسم", "#لون_مخصص", self.دالة_القسم),
    # إضافة المزيد من الأقسام
]
```

### 🔤 إضافة أقسام جديدة / Adding New Sections
1. إضافة القسم الجديد في قائمة `sections`
2. إنشاء دالة جديدة للقسم
3. تخصيص اللون والأيقونة

## 🐛 استكشاف الأخطاء / Troubleshooting

### ❌ مشاكل شائعة / Common Issues

**مشكلة**: النص العربي لا يظهر بشكل صحيح
```bash
# الحل: تأكد من تثبيت مكتبات النص العربي
pip install arabic-reshaper python-bidi
```

**مشكلة**: خطأ في استيراد CustomTkinter
```bash
# الحل: تثبيت أو تحديث CustomTkinter
pip install --upgrade customtkinter
```

**مشكلة**: الواجهة لا تظهر بالشكل المطلوب
```bash
# الحل: تأكد من إصدار Python 3.8+
python --version
```

## 📞 الدعم الفني / Technical Support

- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +966-XX-XXX-XXXX
- 🌐 **الموقع الإلكتروني**: www.setalhall.com
- 💬 **الدردشة المباشرة**: متوفرة في البرنامج

## 📄 الترخيص / License

هذا البرنامج محمي بحقوق الطبع والنشر © 2024 سيت الحل للمحاسبة
This program is protected by copyright © 2024 Set Al-Hal Accounting

## 🔄 التحديثات / Updates

- **الإصدار 1.0**: الإصدار الأولي مع الواجهة الأساسية
- **قريباً**: إضافة قواعد البيانات والتقارير المتقدمة

---

**ملاحظة**: هذا البرنامج في مرحلة التطوير. للحصول على الإصدار الكامل، يرجى التواصل مع فريق التطوير.

**Note**: This program is under development. For the full version, please contact the development team.
