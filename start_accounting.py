#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل برنامج المحاسبة - ملف التشغيل الرئيسي
Start Accounting Program - Main Launcher
"""

import sys
import os

def show_menu():
    """عرض قائمة الواجهات المتاحة"""
    print("=" * 60)
    print("🎯 برنامج سيت الحل للمحاسبة")
    print("🎯 Set Al-Hal Accounting Program")
    print("=" * 60)
    print()
    print("📋 الواجهات المتاحة / Available Interfaces:")
    print()
    print("1️⃣  الواجهة المرئية المحسنة (موصى بها)")
    print("    Enhanced Visible Interface (Recommended)")
    print("    📁 visible_accounting_interface.py")
    print()
    print("2️⃣  واجهة Tkinter العادية")
    print("    Standard Tkinter Interface")
    print("    📁 tkinter_accounting_interface.py")
    print()
    print("3️⃣  الواجهة المطابقة للصورة")
    print("    Exact Replica Interface")
    print("    📁 working_replica_interface.py")
    print()
    print("4️⃣  اختبار النوافذ")
    print("    Test Windows")
    print("    📁 debug_interface.py")
    print()
    print("0️⃣  خروج / Exit")
    print()
    print("=" * 60)

def run_interface(choice):
    """تشغيل الواجهة المختارة"""
    interfaces = {
        "1": ("visible_accounting_interface.py", "الواجهة المرئية المحسنة"),
        "2": ("tkinter_accounting_interface.py", "واجهة Tkinter العادية"),
        "3": ("working_replica_interface.py", "الواجهة المطابقة للصورة"),
        "4": ("debug_interface.py", "اختبار النوافذ")
    }
    
    if choice in interfaces:
        filename, description = interfaces[choice]
        print(f"🚀 تشغيل {description}...")
        print(f"🚀 Starting {description}...")
        
        try:
            # تشغيل الملف
            os.system(f"python {filename}")
            print(f"✅ تم إغلاق {description}")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل {description}: {e}")
            
    elif choice == "0":
        print("👋 وداعاً!")
        print("👋 Goodbye!")
        sys.exit(0)
        
    else:
        print("❌ اختيار غير صحيح!")
        print("❌ Invalid choice!")

def main():
    """الدالة الرئيسية"""
    while True:
        try:
            show_menu()
            choice = input("🔢 اختر رقم الواجهة / Choose interface number: ").strip()
            print()
            
            if choice:
                run_interface(choice)
            else:
                print("❌ يرجى إدخال رقم صحيح!")
                print("❌ Please enter a valid number!")
                
            print()
            input("⏸️  اضغط Enter للمتابعة / Press Enter to continue...")
            print("\n" * 2)
            
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج")
            print("👋 Program stopped")
            break
            
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
            print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
