# مكتبات إضافية للتطبيق
# Additional Application Libraries

# GUI and Interface
customtkinter
tkcalendar

# Arabic Text Processing
arabic-reshaper
python-bidi
pyarabic

# Document and Report Generation
reportlab
fpdf
fpdf2

# Image Processing
pillow

# Database
sqlite-utils

# Date and Time Processing
hijri-converter
babel
pytz
tzlocal

# Build and Packaging
pyinstaller

# File and Archive Processing
pyzipper

# Text and Number Processing
num2words
translate
langdetect

# Data Processing and Analysis
pandas
openpyxl
xlsxwriter
matplotlib
seaborn

# QR Code Generation
pyqrcode

# File System Monitoring
watchdog

# Financial and Geographic Data
forex-python
pycountry

# Security and Encryption
cryptography
pyAesCrypt

# Task Scheduling
schedule

# Text-to-Speech and Audio
pyttsx3
playsound

# Windows-specific Libraries
pywin32

# Note: ctypes is part of Python standard library, no installation needed
