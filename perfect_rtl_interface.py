#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الواجهة المثالية مع دعم RTL - مطابقة تماماً للصورة
Perfect RTL Interface - Exact Match to Image
"""

import tkinter as tk
from tkinter import messagebox

class PerfectRTLInterface:
    def __init__(self):
        """تهيئة الواجهة المثالية مع RTL"""
        print("🎯 إنشاء الواجهة المطابقة تماماً للصورة...")
        self.root = tk.Tk()
        self.setup_window()
        self.create_perfect_interface()
        print("✅ تم إنشاء الواجهة المثالية!")
        
    def setup_window(self):
        """إعداد النافذة المثالي"""
        self.root.title("برنامج سيت الحل للمحاسبة")
        self.root.geometry("1200x800")
        self.root.configure(bg="#2d5a3d")  # الخلفية الخضراء الداكنة
        
        # إظهار النافذة في المقدمة
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = 1200
        height = 800
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_perfect_interface(self):
        """إنشاء الواجهة المطابقة تماماً للصورة"""
        
        # الشريط العلوي الأخضر الفاتح
        top_frame = tk.Frame(self.root, height=100, bg="#4a7c59")
        top_frame.pack(fill="x")
        top_frame.pack_propagate(False)
        
        # شعار البرنامج في الأعلى اليسار (مطابق للصورة)
        logo_frame = tk.Frame(top_frame, width=300, height=80, bg="#2d5a3d", relief="solid", bd=1)
        logo_frame.place(x=20, y=10)
        logo_frame.pack_propagate(False)
        
        # نص الشعار
        logo_text1 = tk.Label(logo_frame, text="برنامج سيت الحل", 
                            font=("Arial", 16, "bold"), bg="#2d5a3d", fg="white")
        logo_text1.place(x=200, y=20)
        
        logo_text2 = tk.Label(logo_frame, text="للمحاسبة", 
                            font=("Arial", 12), bg="#2d5a3d", fg="#90EE90")
        logo_text2.place(x=200, y=45)
        
        # الأيقونات الصغيرة في الشريط العلوي (مطابقة للصورة)
        self.create_top_small_icons(top_frame)
        
        # المنطقة الرئيسية
        main_frame = tk.Frame(self.root, bg="#2d5a3d")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # كلمة "تقارير" في الأعلى اليمين
        reports_label = tk.Label(main_frame, text="تقارير", 
                               font=("Arial", 14, "bold"), bg="#2d5a3d", fg="white")
        reports_label.place(x=1100, y=10)
        
        # الأيقونات الرئيسية مع ترتيب RTL مطابق للصورة
        self.create_main_rtl_grid(main_frame)
        
        # الشريط السفلي
        bottom_frame = tk.Frame(self.root, height=30, bg="#4a7c59")
        bottom_frame.pack(fill="x", side="bottom")
        bottom_frame.pack_propagate(False)
        
        status_text = tk.Label(bottom_frame, text="✅ البرنامج جاهز للاستخدام", 
                             font=("Arial", 10), bg="#4a7c59", fg="white")
        status_text.pack(pady=5)
        
    def create_top_small_icons(self, parent):
        """إنشاء الأيقونات الصغيرة في الأعلى"""
        small_icons = [
            ("مرتجعات", "#FF69B4", 400),
            ("مخزونية", "#32CD32", 500),
            ("محاسبية", "#4169E1", 600),
            ("الخزينة", "#FFD700", 700),
            ("المراجع", "#FF6347", 800),
            ("التقارير", "#9370DB", 900)
        ]
        
        for name, color, x in small_icons:
            icon_btn = tk.Button(parent, text=name, width=8, height=3,
                               bg=color, fg="white", font=("Arial", 8, "bold"),
                               relief="raised", bd=2,
                               command=lambda n=name: self.small_icon_click(n))
            icon_btn.place(x=x, y=20)
            
    def create_main_rtl_grid(self, parent):
        """إنشاء الشبكة الرئيسية مع ترتيب RTL مطابق للصورة"""
        
        # الأيقونات مرتبة من اليمين لليسار كما في الصورة
        main_icons = [
            # الصف الأول (6 أيقونات)
            [
                ("إدخال الحسابات", "#87CEEB"),
                ("إعداد سهل", "#FFA500"), 
                ("جرد مخزن", "#20B2AA"),
                ("إدخال الأصناف", "#FFD700"),
                ("صرف", "#9370DB"),
                ("شراء", "#4682B4")
            ],
            # الصف الثاني (5 أيقونات)
            [
                ("مرتجع شراء", "#228B22"),
                ("تسوية مخزن", "#32CD32"),
                ("تحويل لمخزن", "#DC143C"),
                ("قبض", "#FF6347"),
                ("بيع", "#40E0D0")
            ],
            # الصف الثالث (6 أيقونات)
            [
                ("مرتجع بيع", "#8A2BE2"),
                ("عرض أسعار", "#20B2AA"),
                ("الفواتير", "#FF69B4"),
                ("الخزينة", "#9370DB"),
                ("الحسابات", "#4169E1"),
                ("البضاعة", "#32CD32")
            ]
        ]
        
        # إعدادات الشبكة
        start_x = 1000  # البداية من اليمين
        start_y = 60
        icon_size = 130
        gap_x = 160
        gap_y = 150
        
        for row_idx, row in enumerate(main_icons):
            for col_idx, (name, color) in enumerate(row):
                # حساب الموضع من اليمين لليسار
                x = start_x - (col_idx * gap_x)
                y = start_y + (row_idx * gap_y)
                
                # إنشاء الأيقونة
                self.create_main_icon(parent, name, color, x, y, icon_size)
                
    def create_main_icon(self, parent, name, color, x, y, size):
        """إنشاء أيقونة رئيسية"""
        
        # إطار الأيقونة
        icon_frame = tk.Frame(parent, width=size, height=size, 
                            bg=color, relief="raised", bd=3)
        icon_frame.place(x=x, y=y)
        icon_frame.pack_propagate(False)
        
        # الزر الرئيسي
        main_btn = tk.Button(icon_frame, text="📊", font=("Arial", 32),
                           bg=color, fg="white", relief="flat",
                           activebackground=self.get_darker_color(color),
                           command=lambda: self.main_icon_click(name))
        main_btn.pack(fill="both", expand=True)
        
        # النص أسفل الأيقونة
        text_label = tk.Label(parent, text=name, font=("Arial", 11, "bold"),
                            bg="#2d5a3d", fg="white")
        text_label.place(x=x + size//2, y=y + size + 8, anchor="center")
        
    def get_darker_color(self, color):
        """الحصول على لون أغمق للتأثير التفاعلي"""
        darker_colors = {
            "#87CEEB": "#4682B4", "#FFA500": "#FF8C00", "#20B2AA": "#008B8B",
            "#FFD700": "#DAA520", "#9370DB": "#8A2BE2", "#4682B4": "#4169E1",
            "#228B22": "#006400", "#32CD32": "#228B22", "#DC143C": "#B22222",
            "#FF6347": "#CD5C5C", "#40E0D0": "#20B2AA", "#8A2BE2": "#7B68EE",
            "#FF69B4": "#FF1493", "#4169E1": "#0000CD"
        }
        return darker_colors.get(color, color)
        
    def small_icon_click(self, name):
        """معالج النقر على الأيقونات الصغيرة"""
        messagebox.showinfo("قسم صغير", f"تم النقر على: {name}\nقسم فرعي قيد التطوير")
        
    def main_icon_click(self, name):
        """معالج النقر على الأيقونات الرئيسية"""
        messagebox.showinfo(
            f"قسم {name}",
            f"🎯 تم فتح قسم: {name}\n\n"
            f"📋 الوظائف المتاحة:\n"
            f"• عرض البيانات\n"
            f"• إضافة جديد\n" 
            f"• تعديل الموجود\n"
            f"• طباعة التقارير\n\n"
            f"✨ الواجهة مطابقة للصورة مع دعم RTL"
        )
        
    def run(self):
        """تشغيل التطبيق"""
        print("🖥️ عرض الواجهة المثالية مع دعم RTL...")
        print("📸 مطابقة تماماً للصورة المرفقة!")
        print("🔄 ترتيب الأيقونات: من اليمين إلى اليسار")
        self.root.mainloop()
        print("👋 تم إغلاق البرنامج")

if __name__ == "__main__":
    try:
        app = PerfectRTLInterface()
        app.run()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
