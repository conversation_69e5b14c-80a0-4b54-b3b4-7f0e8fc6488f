#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للواجهة
Simple Interface Test
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox

# إعداد المظهر
ctk.set_appearance_mode("dark")

class SimpleTestInterface:
    def __init__(self):
        """تهيئة الواجهة البسيطة"""
        self.root = ctk.CTk()
        self.setup_window()
        self.create_interface()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("اختبار الواجهة")
        self.root.geometry("800x600")
        self.root.configure(fg_color="#2d5a3d")
        
    def create_interface(self):
        """إنشاء الواجهة"""
        # عنوان
        title = ctk.CTkLabel(self.root, 
                           text="برنامج المحاسبة يعمل بنجاح!",
                           font=ctk.CTkFont(size=24, weight="bold"),
                           text_color="white")
        title.pack(pady=50)
        
        # زر اختبار
        test_btn = ctk.CTkButton(self.root,
                               text="اختبار",
                               width=200,
                               height=50,
                               font=ctk.CTkFont(size=16),
                               command=self.test_click)
        test_btn.pack(pady=20)
        
        # رسالة نجاح
        success_label = ctk.CTkLabel(self.root,
                                   text="✅ تم تحميل البرنامج بنجاح!",
                                   font=ctk.CTkFont(size=16),
                                   text_color="#90EE90")
        success_label.pack(pady=20)
        
    def test_click(self):
        """اختبار النقر"""
        messagebox.showinfo("نجح!", "البرنامج يعمل بشكل مثالي!")
        
    def run(self):
        """تشغيل التطبيق"""
        print("🚀 تشغيل الاختبار...")
        self.root.mainloop()
        print("✅ تم إغلاق البرنامج")

if __name__ == "__main__":
    app = SimpleTestInterface()
    app.run()
