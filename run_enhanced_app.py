#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل الواجهة المحسنة لبرنامج المحاسبة
Run Enhanced Accounting Interface
"""

import sys
import os

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def main():
    """تشغيل الواجهة المحسنة"""
    print("🚀 تشغيل الواجهة المحسنة لبرنامج المحاسبة...")
    print("🚀 Starting Enhanced Accounting Interface...")
    
    try:
        from enhanced_accounting_interface import EnhancedAccountingInterface
        
        print("✅ تم تحميل الواجهة المحسنة بنجاح!")
        print("✅ Enhanced interface loaded successfully!")
        
        # إنشاء وتشغيل التطبيق
        app = EnhancedAccountingInterface()
        app.run()
        
        print("👋 تم إغلاق البرنامج")
        print("👋 Program closed")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print(f"❌ Import error: {e}")
        print("\n📋 تأكد من تثبيت المكتبات المطلوبة:")
        print("📋 Make sure to install required libraries:")
        print("pip install customtkinter arabic-reshaper python-bidi")
        input("اضغط Enter للخروج / Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        print(f"❌ Unexpected error: {e}")
        input("اضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
