#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة محاسبة محسنة مع أيقونات وتأثيرات بصرية
Enhanced Accounting Interface with Icons and Visual Effects
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from datetime import datetime
import arabic_reshaper
from bidi.algorithm import get_display

# إعداد المظهر العام
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class EnhancedAccountingInterface:
    def __init__(self):
        """تهيئة الواجهة المحسنة"""
        self.root = ctk.CTk()
        self.setup_window()
        self.create_header()
        self.create_main_dashboard()
        self.create_footer()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("برنامج سيت الحل للمحاسبة - الإصدار المحسن")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
    def arabic_text(self, text):
        """تحويل النص العربي للعرض الصحيح"""
        reshaped_text = arabic_reshaper.reshape(text)
        return get_display(reshaped_text)
        
    def create_header(self):
        """إنشاء الرأس المحسن"""
        header_frame = ctk.CTkFrame(self.root, height=80, corner_radius=0, 
                                   fg_color=["#1f538d", "#14375e"])
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # شعار وعنوان البرنامج
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="left", fill="y", padx=20)
        
        main_title = ctk.CTkLabel(title_frame, 
                                text=self.arabic_text("💼 برنامج سيت الحل للمحاسبة"), 
                                font=ctk.CTkFont(size=24, weight="bold"),
                                text_color="white")
        main_title.pack(anchor="w", pady=(15, 5))
        
        subtitle = ctk.CTkLabel(title_frame, 
                              text=self.arabic_text("نظام محاسبي متكامل وحديث"), 
                              font=ctk.CTkFont(size=12),
                              text_color="#b8d4f0")
        subtitle.pack(anchor="w")
        
        # معلومات المستخدم
        user_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        user_frame.pack(side="right", fill="y", padx=20)
        
        welcome_label = ctk.CTkLabel(user_frame, 
                                   text=self.arabic_text("👤 أهلاً وسهلاً: أحمد محمد"), 
                                   font=ctk.CTkFont(size=14, weight="bold"),
                                   text_color="white")
        welcome_label.pack(anchor="e", pady=(10, 2))
        
        company_label = ctk.CTkLabel(user_frame, 
                                   text=self.arabic_text("🏢 الشركة: شركة الحل للتجارة"), 
                                   font=ctk.CTkFont(size=12),
                                   text_color="#b8d4f0")
        company_label.pack(anchor="e")
        
        license_label = ctk.CTkLabel(user_frame, 
                                   text=self.arabic_text("✅ الترخيص: نشط حتى 2025/12/31"), 
                                   font=ctk.CTkFont(size=10),
                                   text_color="#90ee90")
        license_label.pack(anchor="e")
        
    def create_main_dashboard(self):
        """إنشاء لوحة التحكم الرئيسية"""
        # إطار التمرير الرئيسي
        main_scroll = ctk.CTkScrollableFrame(self.root, fg_color="transparent")
        main_scroll.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان الأقسام
        sections_title = ctk.CTkLabel(main_scroll, 
                                    text=self.arabic_text("📊 الأقسام الرئيسية"), 
                                    font=ctk.CTkFont(size=22, weight="bold"))
        sections_title.pack(pady=(0, 30))
        
        # تعريف الأقسام مع الأيقونات والألوان المحسنة
        sections_data = [
            # الصف الأول - الأقسام الأساسية
            [
                ("📈 التقارير", "تقارير مالية شاملة", "#2E8B57", "#228B22"),
                ("🧾 الفواتير", "إدارة الفواتير والمبيعات", "#4169E1", "#1E90FF"),
                ("💰 الخزينة", "إدارة النقدية والخزينة", "#20B2AA", "#008B8B"),
                ("📋 الحسابات", "إدارة الحسابات والعملاء", "#FFD700", "#FFA500"),
                ("📦 البضاعة", "إدارة المخزون والمنتجات", "#9370DB", "#8A2BE2"),
                ("📊 التحليلات", "تحليل البيانات المالية", "#4682B4", "#5F9EA0")
            ],
            # الصف الثاني - العمليات اليومية
            [
                ("➕ إدخال الحسابات", "إضافة حسابات جديدة", "#32CD32", "#228B22"),
                ("⚙️ الإعدادات", "تخصيص البرنامج", "#FF6347", "#DC143C"),
                ("📊 الجرد", "عمليات جرد المخزون", "#8A2BE2", "#9400D3"),
                ("💸 الصرف", "عمليات الصرف والمدفوعات", "#FF4500", "#FF6347"),
                ("📈 المؤشرات", "مؤشرات الأداء المالي", "#00CED1", "#20B2AA")
            ],
            # الصف الثالث - العمليات المتقدمة
            [
                ("🔄 مرتجع الشراء", "إدارة مرتجعات الشراء", "#228B22", "#32CD32"),
                ("💰 عرض الأسعار", "إنشاء عروض الأسعار", "#4169E1", "#6495ED"),
                ("📦 مرتجع الجرد", "إدارة مرتجعات الجرد", "#9370DB", "#BA55D3"),
                ("💵 القبض", "عمليات القبض والتحصيل", "#FF8C00", "#FFA500"),
                ("🚚 تحويل المخزن", "نقل البضائع بين المخازن", "#20B2AA", "#48D1CC"),
                ("⚖️ تسوية المخزن", "تسوية أرصدة المخزون", "#32CD32", "#7CFC00")
            ]
        ]
        
        # إنشاء الأقسام
        for row_index, row_data in enumerate(sections_data):
            self.create_section_row(main_scroll, row_data, row_index)
            
    def create_section_row(self, parent, sections, row_index):
        """إنشاء صف من الأقسام"""
        row_frame = ctk.CTkFrame(parent, fg_color="transparent")
        row_frame.pack(fill="x", pady=15)
        
        # حساب عدد الأعمدة
        cols_count = len(sections)
        
        for col_index, (title, description, color, hover_color) in enumerate(sections):
            self.create_enhanced_button(row_frame, title, description, color, hover_color, 
                                      col_index, cols_count)
            
    def create_enhanced_button(self, parent, title, description, color, hover_color, 
                             col_index, total_cols):
        """إنشاء زر محسن مع تأثيرات بصرية"""
        # إطار الزر
        button_frame = ctk.CTkFrame(parent, fg_color="transparent")
        
        # تحديد موضع الزر
        if total_cols <= 3:
            button_frame.pack(side="right" if col_index == 0 else "left", 
                            padx=15, pady=10, expand=True)
        else:
            button_frame.pack(side="left", padx=10, pady=10, expand=True)
        
        # الزر الرئيسي
        main_button = ctk.CTkButton(
            button_frame,
            text="",  # سنضع النص في label منفصل
            width=200,
            height=140,
            fg_color=color,
            hover_color=hover_color,
            corner_radius=20,
            border_width=2,
            border_color="white",
            command=lambda: self.section_clicked(title)
        )
        main_button.pack()
        
        # إطار النص داخل الزر
        text_frame = ctk.CTkFrame(main_button, fg_color="transparent")
        text_frame.place(relx=0.5, rely=0.5, anchor="center")
        
        # عنوان القسم
        title_label = ctk.CTkLabel(text_frame, 
                                 text=self.arabic_text(title), 
                                 font=ctk.CTkFont(size=16, weight="bold"),
                                 text_color="white")
        title_label.pack(pady=(5, 2))
        
        # وصف القسم
        desc_label = ctk.CTkLabel(text_frame, 
                                text=self.arabic_text(description), 
                                font=ctk.CTkFont(size=10),
                                text_color="#f0f0f0",
                                wraplength=180)
        desc_label.pack(pady=(0, 5))
        
    def create_footer(self):
        """إنشاء التذييل المحسن"""
        footer_frame = ctk.CTkFrame(self.root, height=50, corner_radius=0,
                                  fg_color=["#1f538d", "#14375e"])
        footer_frame.pack(fill="x", side="bottom")
        footer_frame.pack_propagate(False)
        
        # الوقت والتاريخ
        time_frame = ctk.CTkFrame(footer_frame, fg_color="transparent")
        time_frame.pack(side="left", fill="y", padx=15)
        
        self.time_label = ctk.CTkLabel(time_frame, 
                                     text=self.get_formatted_time(), 
                                     font=ctk.CTkFont(size=12, weight="bold"),
                                     text_color="white")
        self.time_label.pack(anchor="w", pady=15)
        
        # حالة الاتصال
        status_frame = ctk.CTkFrame(footer_frame, fg_color="transparent")
        status_frame.pack(side="left", fill="y", padx=20)
        
        status_label = ctk.CTkLabel(status_frame, 
                                  text=self.arabic_text("🟢 متصل | 🔒 آمن | ⚡ سريع"), 
                                  font=ctk.CTkFont(size=11),
                                  text_color="#90ee90")
        status_label.pack(anchor="w", pady=15)
        
        # أزرار سريعة محسنة
        buttons_frame = ctk.CTkFrame(footer_frame, fg_color="transparent")
        buttons_frame.pack(side="right", fill="y", padx=15)
        
        # زر تبديل الوضع
        theme_btn = ctk.CTkButton(buttons_frame, text="🌙/☀️", width=50, height=35,
                                corner_radius=10, command=self.toggle_theme)
        theme_btn.pack(side="right", padx=5, pady=7)
        
        # زر خدمة العملاء
        support_btn = ctk.CTkButton(buttons_frame, 
                                  text=self.arabic_text("📞 الدعم"), 
                                  width=80, height=35, corner_radius=10,
                                  command=self.customer_support)
        support_btn.pack(side="right", padx=5, pady=7)
        
        # زر الاشتراك
        subscription_btn = ctk.CTkButton(buttons_frame, 
                                       text=self.arabic_text("💳 اشتراك"), 
                                       width=80, height=35, corner_radius=10,
                                       command=self.subscription_info)
        subscription_btn.pack(side="right", padx=5, pady=7)
        
    def get_formatted_time(self):
        """الحصول على الوقت والتاريخ منسق"""
        now = datetime.now()
        date_str = now.strftime("%Y/%m/%d")
        time_str = now.strftime("%H:%M:%S")
        return f"📅 {date_str} | ⏰ {time_str}"
        
    def update_time(self):
        """تحديث الوقت كل ثانية"""
        if hasattr(self, 'time_label'):
            self.time_label.configure(text=self.get_formatted_time())
        self.root.after(1000, self.update_time)
        
    def section_clicked(self, section_title):
        """معالج النقر على الأقسام"""
        clean_title = section_title.split(" ", 1)[-1]  # إزالة الأيقونة
        messagebox.showinfo(
            self.arabic_text("قسم") + f" {clean_title}", 
            self.arabic_text(f"سيتم فتح قسم {clean_title}\nهذه الميزة قيد التطوير...")
        )
        
    def toggle_theme(self):
        """تبديل الوضع الليلي/النهاري"""
        current_mode = ctk.get_appearance_mode()
        new_mode = "light" if current_mode == "Dark" else "dark"
        ctk.set_appearance_mode(new_mode)
        
    def customer_support(self):
        """خدمة العملاء"""
        messagebox.showinfo(
            self.arabic_text("خدمة العملاء"), 
            self.arabic_text("📞 الهاتف: +966-XX-XXX-XXXX\n📧 البريد: <EMAIL>\n💬 الدردشة: متوفرة 24/7")
        )
        
    def subscription_info(self):
        """معلومات الاشتراك"""
        messagebox.showinfo(
            self.arabic_text("معلومات الاشتراك"), 
            self.arabic_text("💳 نوع الاشتراك: بريميوم\n📅 تاريخ الانتهاء: 2025/12/31\n✅ الحالة: نشط")
        )
        
    def run(self):
        """تشغيل التطبيق"""
        self.update_time()
        self.root.mainloop()

if __name__ == "__main__":
    app = EnhancedAccountingInterface()
    app.run()
