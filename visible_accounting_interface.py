#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المحاسبة المرئية - تظهر في المقدمة
Visible Accounting Interface - Shows in Foreground
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox

# إعداد المظهر
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class VisibleAccountingInterface:
    def __init__(self):
        """تهيئة الواجهة المرئية"""
        print("🚀 بدء تشغيل برنامج المحاسبة...")
        self.root = ctk.CTk()
        self.setup_window()
        self.create_interface()
        print("✅ تم تحميل الواجهة بنجاح!")
        
    def setup_window(self):
        """إعداد النافذة لتظهر في المقدمة"""
        self.root.title("برنامج سيت الحل للمحاسبة")
        self.root.geometry("1024x768")
        self.root.configure(fg_color="#2d5a3d")
        
        # جعل النافذة تظهر في المقدمة
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))
        
        # وضع النافذة في وسط الشاشة
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_interface(self):
        """إنشاء الواجهة مطابقة للصورة"""
        
        # الشريط العلوي
        top_frame = ctk.CTkFrame(self.root, height=120, corner_radius=0, 
                               fg_color="#4a7c59")
        top_frame.pack(fill="x", padx=0, pady=0)
        top_frame.pack_propagate(False)
        
        # شعار البرنامج
        logo_frame = ctk.CTkFrame(top_frame, width=300, height=100, 
                                corner_radius=10, fg_color="#2d5a3d")
        logo_frame.place(x=20, y=10)
        logo_frame.pack_propagate(False)
        
        # النص الرئيسي
        main_text = ctk.CTkLabel(logo_frame, 
                               text="برنامج سيت الحل",
                               font=ctk.CTkFont(size=20, weight="bold"),
                               text_color="white")
        main_text.place(x=150, y=25)
        
        sub_text = ctk.CTkLabel(logo_frame,
                              text="للمحاسبة",
                              font=ctk.CTkFont(size=16),
                              text_color="#90EE90")
        sub_text.place(x=150, y=55)
        
        # رسالة ترحيب
        welcome_label = ctk.CTkLabel(top_frame,
                                   text="🎉 مرحباً بك في برنامج المحاسبة",
                                   font=ctk.CTkFont(size=14, weight="bold"),
                                   text_color="white")
        welcome_label.place(x=400, y=50)
        
        # الأيقونات الصغيرة في الأعلى
        self.create_top_icons(top_frame)
        
        # المنطقة الرئيسية
        main_area = ctk.CTkFrame(self.root, fg_color="#2d5a3d")
        main_area.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان الأقسام
        title_label = ctk.CTkLabel(main_area, 
                                 text="📊 الأقسام الرئيسية",
                                 font=ctk.CTkFont(size=18, weight="bold"),
                                 text_color="white")
        title_label.pack(pady=(10, 20))
        
        # شبكة الأيقونات الرئيسية
        self.create_main_icons_grid(main_area)
        
        # شريط الحالة
        status_frame = ctk.CTkFrame(self.root, height=40, corner_radius=0,
                                  fg_color="#4a7c59")
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)
        
        status_label = ctk.CTkLabel(status_frame,
                                  text="✅ البرنامج جاهز للاستخدام | 🖱️ انقر على أي أيقونة للبدء",
                                  font=ctk.CTkFont(size=12),
                                  text_color="white")
        status_label.pack(pady=10)
        
    def create_top_icons(self, parent):
        """الأيقونات الصغيرة في الأعلى"""
        top_icons = [
            ("مرتجعات", "#FF69B4", 450),
            ("مخزونية", "#32CD32", 550),
            ("محاسبية", "#4169E1", 650),
            ("الخزينة", "#FFD700", 750),
            ("المراجع", "#FF6347", 850),
            ("التقارير", "#9370DB", 950)
        ]
        
        for name, color, x_pos in top_icons:
            icon_frame = ctk.CTkFrame(parent, width=70, height=70, 
                                    corner_radius=8, fg_color=color,
                                    border_width=1, border_color="white")
            icon_frame.place(x=x_pos, y=25)
            icon_frame.pack_propagate(False)
            
            # زر قابل للنقر
            btn = ctk.CTkButton(icon_frame, text="", width=68, height=68,
                              fg_color="transparent", hover_color="transparent",
                              command=lambda n=name: self.small_icon_clicked(n))
            btn.pack(fill="both", expand=True)
            
            label = ctk.CTkLabel(icon_frame, 
                               text=name,
                               font=ctk.CTkFont(size=9, weight="bold"),
                               text_color="white",
                               wraplength=60)
            label.place(relx=0.5, rely=0.5, anchor="center")
            
    def create_main_icons_grid(self, parent):
        """شبكة الأيقونات الرئيسية"""
        
        # إطار للشبكة
        grid_frame = ctk.CTkFrame(parent, fg_color="transparent")
        grid_frame.pack(expand=True, fill="both", padx=50, pady=20)
        
        # تعريف الأيقونات
        icons_data = [
            # الصف الأول
            [
                ("إدخال الحسابات", "#87CEEB"),
                ("إعداد", "#FFA500"),
                ("شكل وتصنيف", "#20B2AA"),
                ("إدخال الأصناف", "#FFD700"),
                ("الحركة التوريدية", "#9370DB"),
                ("تحليل البيانات", "#4682B4")
            ],
            # الصف الثاني
            [
                ("مخزن", "#FFA500"),
                ("بيع", "#32CD32"),
                ("قبض", "#DC143C"),
                ("صرف", "#FF6347"),
                ("مؤشرات", "#40E0D0"),
                ("", "")  # خانة فارغة
            ],
            # الصف الثالث
            [
                ("مرتجع شراء", "#228B22"),
                ("عرض أسعار", "#20B2AA"),
                ("مرتجع جرد", "#8A2BE2"),
                ("قبض", "#9370DB"),
                ("تحويل لمخزن", "#4169E1"),
                ("تسوية مخزن", "#32CD32")
            ]
        ]
        
        # إنشاء الشبكة
        for row_idx, row in enumerate(icons_data):
            for col_idx, (name, color) in enumerate(row):
                if name:  # إذا لم تكن خانة فارغة
                    self.create_grid_icon(grid_frame, name, color, row_idx, col_idx)
                    
    def create_grid_icon(self, parent, name, color, row, col):
        """إنشاء أيقونة في الشبكة"""
        
        # إطار الأيقونة
        icon_frame = ctk.CTkFrame(parent, width=140, height=140, 
                                corner_radius=15, fg_color=color,
                                border_width=2, border_color="white")
        icon_frame.grid(row=row*2, column=col, padx=10, pady=10, sticky="nsew")
        icon_frame.pack_propagate(False)
        
        # زر قابل للنقر
        button = ctk.CTkButton(icon_frame, text="", width=136, height=136,
                             fg_color="transparent", hover_color="transparent",
                             corner_radius=13,
                             command=lambda: self.main_icon_clicked(name))
        button.pack(fill="both", expand=True)
        
        # النص تحت الأيقونة
        text_label = ctk.CTkLabel(parent, 
                                text=name,
                                font=ctk.CTkFont(size=12, weight="bold"),
                                text_color="white")
        text_label.grid(row=row*2+1, column=col, pady=(5, 15))
        
    def small_icon_clicked(self, icon_name):
        """معالج النقر على الأيقونات الصغيرة"""
        messagebox.showinfo(
            f"قسم {icon_name}",
            f"🎯 تم النقر على: {icon_name}\n\n✨ هذا القسم قيد التطوير\n🚀 سيتم إضافة المزيد من الميزات قريباً"
        )
        
    def main_icon_clicked(self, icon_name):
        """معالج النقر على الأيقونات الرئيسية"""
        messagebox.showinfo(
            f"قسم {icon_name}",
            f"🎯 تم فتح قسم: {icon_name}\n\n📋 الوظائف المتاحة:\n• عرض البيانات\n• إضافة جديد\n• تعديل الموجود\n• طباعة التقارير\n\n✨ قيد التطوير..."
        )
        
    def run(self):
        """تشغيل التطبيق"""
        print("🖥️ عرض النافذة...")
        print("📱 يجب أن تظهر النافذة الآن في المقدمة!")
        self.root.mainloop()
        print("👋 تم إغلاق البرنامج")

if __name__ == "__main__":
    try:
        app = VisibleAccountingInterface()
        app.run()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
