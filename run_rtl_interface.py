#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل الواجهة مع دعم الاتجاه من اليمين إلى اليسار
Run RTL (Right-to-Left) Interface
"""

import sys
import os

def main():
    """تشغيل الواجهة مع دعم RTL"""
    print("🎯 تشغيل واجهة المحاسبة مع دعم RTL...")
    print("🎯 Starting Accounting Interface with RTL Support...")
    print("📸 مطابقة تماماً للصورة المرفقة")
    print("📸 Exact match to the provided image")
    print("=" * 60)
    
    try:
        from rtl_accounting_interface import RTLAccountingInterface
        
        print("✅ تم تحميل الواجهة مع دعم RTL بنجاح!")
        print("✅ RTL interface loaded successfully!")
        print("🔄 الترتيب: من اليمين إلى اليسار")
        print("🔄 Layout: Right-to-Left")
        print("🚀 بدء التشغيل...")
        print("🚀 Starting application...")
        
        # إنشاء وتشغيل التطبيق
        app = RTLAccountingInterface()
        app.run()
        
        print("👋 تم إغلاق البرنامج بنجاح")
        print("👋 Program closed successfully")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print(f"❌ Import error: {e}")
        print("\n📋 المكتبات المطلوبة:")
        print("📋 Required libraries:")
        print("• tkinter (مدمج مع Python)")
        print("• arabic-reshaper (اختياري)")
        print("• python-bidi (اختياري)")
        print("\n💡 لتثبيت المكتبات الاختيارية:")
        print("💡 To install optional libraries:")
        print("pip install arabic-reshaper python-bidi")
        input("اضغط Enter للخروج / Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        print(f"❌ Unexpected error: {e}")
        input("اضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
