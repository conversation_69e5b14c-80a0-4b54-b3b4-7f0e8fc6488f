#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل برنامج المحاسبة المالية
Run file for Financial Accounting Program

هذا الملف لتشغيل واختبار واجهة برنامج المحاسبة
This file is for running and testing the accounting program interface
"""

import sys
import os

# إضافة المسار الحالي لمسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from accounting_main_interface import AccountingMainInterface
    
    def main():
        """الدالة الرئيسية لتشغيل البرنامج"""
        print("🚀 بدء تشغيل برنامج المحاسبة المالية...")
        print("📊 Financial Accounting Program Starting...")
        
        # إنشاء وتشغيل التطبيق
        app = AccountingMainInterface()
        
        print("✅ تم تحميل الواجهة بنجاح!")
        print("✅ Interface loaded successfully!")
        
        # تشغيل التطبيق
        app.run()
        
        print("👋 تم إغلاق البرنامج")
        print("👋 Program closed")

    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print(f"❌ Import error: {e}")
    print("\n📋 تأكد من تثبيت المكتبات المطلوبة:")
    print("📋 Make sure to install required libraries:")
    print("pip install customtkinter arabic-reshaper python-bidi")
    
except Exception as e:
    print(f"❌ خطأ غير متوقع: {e}")
    print(f"❌ Unexpected error: {e}")
    input("اضغط Enter للخروج / Press Enter to exit...")
