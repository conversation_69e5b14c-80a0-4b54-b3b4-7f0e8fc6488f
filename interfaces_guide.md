# 🎯 دليل واجهات برنامج المحاسبة
# Accounting Program Interfaces Guide

## 📋 الواجهات المتوفرة / Available Interfaces

لديك الآن **3 واجهات مختلفة** لبرنامج المحاسبة:

You now have **3 different interfaces** for the accounting program:

---

## 1️⃣ الواجهة المطابقة للصورة (الموصى بها)
## Exact Replica Interface (Recommended)

**الملف**: `exact_replica_interface.py`  
**التشغيل**: `python run_exact_replica.py`

### ✨ المميزات:
- ✅ **مطابقة تماماً للصورة المرفقة**
- ✅ خلفية خضراء داكنة مطابقة
- ✅ شعار "برنامج سيت الحل للمحاسبة" في المكان الصحيح
- ✅ أيقونات صغيرة في الأعلى
- ✅ شبكة الأيقونات الرئيسية 6×3
- ✅ ألوان مطابقة للصورة
- ✅ أسماء الأقسام تحت كل أيقونة

### 🎨 التصميم:
- **الخلفية**: أخضر داكن `#2d5a3d`
- **الشريط العلوي**: أخضر فاتح `#4a7c59`
- **الأيقونات**: ألوان متنوعة مطابقة للصورة
- **النصوص**: باللغة العربية مع محاذاة صحيحة

---

## 2️⃣ الواجهة الأساسية
## Basic Interface

**الملف**: `accounting_main_interface.py`  
**التشغيل**: `python run_accounting_app.py`

### ✨ المميزات:
- ✅ تصميم بسيط ومباشر
- ✅ سهولة في الاستخدام
- ✅ أداء سريع
- ✅ مناسب للمبتدئين

---

## 3️⃣ الواجهة المحسنة
## Enhanced Interface

**الملف**: `enhanced_accounting_interface.py`  
**التشغيل**: `python run_enhanced_app.py`

### ✨ المميزات:
- ✅ تصميم احترافي وعصري
- ✅ أيقونات تعبيرية (إيموجي)
- ✅ وصف تفصيلي لكل قسم
- ✅ تأثيرات بصرية متقدمة

---

## 🎯 أيهما تختار؟

### للحصول على نفس الصورة تماماً:
```bash
python run_exact_replica.py
```
**هذه هي الواجهة المطابقة تماماً للصورة التي أرفقتها!**

### للاستخدام اليومي البسيط:
```bash
python run_accounting_app.py
```

### للمظهر الاحترافي المتقدم:
```bash
python run_enhanced_app.py
```

---

## 📊 الأقسام المتوفرة في جميع الواجهات

### 🏢 الأقسام الرئيسية:
- 📈 **التقارير** - تقارير مالية شاملة
- 🧾 **الفواتير** - إدارة الفواتير والمبيعات
- 💰 **الخزينة** - إدارة النقدية والخزينة
- 📋 **الحسابات** - إدارة الحسابات والعملاء
- 📦 **البضاعة** - إدارة المخزون والمنتجات

### 🔧 العمليات اليومية:
- ➕ **إدخال الحسابات** - إضافة حسابات جديدة
- ⚙️ **الإعدادات** - تخصيص البرنامج
- 📊 **الجرد** - عمليات جرد المخزون
- 💸 **الصرف** - عمليات الصرف والمدفوعات
- 📈 **المؤشرات** - مؤشرات الأداء المالي

### 🔄 العمليات المتقدمة:
- 🔄 **مرتجع الشراء** - إدارة مرتجعات الشراء
- 💰 **عرض الأسعار** - إنشاء عروض الأسعار
- 📦 **مرتجع الجرد** - إدارة مرتجعات الجرد
- 💵 **القبض** - عمليات القبض والتحصيل
- 🚚 **تحويل المخزن** - نقل البضائع بين المخازن
- ⚖️ **تسوية المخزن** - تسوية أرصدة المخزون

---

## 🛠️ متطلبات التشغيل

### 📋 المكتبات المطلوبة:
```bash
pip install customtkinter>=5.2.0
pip install arabic-reshaper>=3.0.0
pip install python-bidi>=0.6.6
```

### 💻 متطلبات النظام:
- **نظام التشغيل**: Windows 10/11
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM كحد أدنى

---

## 🚀 التشغيل السريع

### للواجهة المطابقة للصورة (الأفضل):
```bash
python run_exact_replica.py
```

### أو التشغيل المباشر:
```bash
python exact_replica_interface.py
```

---

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **تأكد من تثبيت المكتبات**:
   ```bash
   pip install -r requirements_additional.txt
   ```

2. **تحقق من إصدار Python**:
   ```bash
   python --version
   ```

3. **في حالة الأخطاء**:
   - تأكد من أن جميع الملفات في نفس المجلد
   - أعد تشغيل الكمبيوتر إذا لزم الأمر
   - تأكد من أن Windows Defender لا يحجب البرنامج

---

## 🎉 الخلاصة

**الواجهة المطابقة للصورة** (`exact_replica_interface.py`) هي الأقرب لما طلبته وتحتوي على:

- ✅ نفس الألوان والتخطيط
- ✅ نفس ترتيب الأيقونات
- ✅ نفس أسماء الأقسام
- ✅ نفس الشكل العام

**جرب الآن**: `python run_exact_replica.py`

---

**ملاحظة**: جميع الواجهات تدعم اللغة العربية بالكامل وتعمل على نظام Windows بشكل مثالي! 🇸🇦
