# متطلبات قارئ الباركود
# Barcode Scanner Requirements

# Core barcode and image processing libraries
# مكتبة OpenCV لمعالجة الصور والكاميرا
opencv-python>=4.8.0

# مكتبة pyzbar لقراءة الباركود
pyzbar>=0.1.9

# مكتبة إنشاء الباركود
python-barcode[images]>=0.15.0

# مكتبة Pillow لمعالجة الصور (مطلوبة لإنشاء الباركود)
Pillow>=10.0.0

# QR code generation
qrcode>=7.4.0

# Data processing and scientific libraries
# مكتبات إضافية قد تكون مطلوبة
numpy>=1.24.0
pandas>=2.1.0
scipy>=1.11.0
scikit-learn>=1.3.0

# GUI and visualization
customtkinter>=5.2.0
matplotlib>=3.8.0

# File processing and reporting
openpyxl>=3.1.0
xlsxwriter>=3.1.0
reportlab>=4.0.0
lxml>=4.9.0

# Database connectivity
pyodbc>=4.0.0
psycopg2-binary>=2.9.0

# Scheduling and networking
apscheduler>=3.10.0
requests>=2.31.0
