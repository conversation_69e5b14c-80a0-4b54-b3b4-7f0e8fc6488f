#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل الواجهة المطابقة تماماً للصورة الأصلية
Run Exact Replica Interface
"""

import sys
import os

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def main():
    """تشغيل الواجهة المطابقة للصورة"""
    print("🎯 تشغيل الواجهة المطابقة تماماً للصورة الأصلية...")
    print("🎯 Starting Exact Replica Interface...")
    
    try:
        from exact_replica_interface import ExactReplicaInterface
        
        print("✅ تم تحميل الواجهة المطابقة بنجاح!")
        print("✅ Exact replica interface loaded successfully!")
        print("📸 الواجهة مطابقة تماماً للصورة المرفقة")
        print("📸 Interface matches exactly the provided image")
        
        # إنشاء وتشغيل التطبيق
        app = ExactReplicaInterface()
        app.run()
        
        print("👋 تم إغلاق البرنامج")
        print("👋 Program closed")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print(f"❌ Import error: {e}")
        print("\n📋 تأكد من تثبيت المكتبات المطلوبة:")
        print("📋 Make sure to install required libraries:")
        print("pip install customtkinter arabic-reshaper python-bidi")
        input("اضغط Enter للخروج / Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        print(f"❌ Unexpected error: {e}")
        input("اضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
