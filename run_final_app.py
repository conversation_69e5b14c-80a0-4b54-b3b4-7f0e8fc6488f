#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البرنامج النهائي - الواجهة المطابقة للصورة
Run Final App - Exact Replica Interface
"""

import sys
import os

def main():
    """تشغيل البرنامج النهائي"""
    print("🎯 تشغيل برنامج المحاسبة المطابق للصورة...")
    print("🎯 Starting Accounting Program - Exact Replica...")
    
    try:
        from working_replica_interface import WorkingReplicaInterface
        
        print("✅ تم تحميل الواجهة بنجاح!")
        print("✅ Interface loaded successfully!")
        print("📸 الواجهة مطابقة تماماً للصورة المرفقة")
        print("📸 Interface matches exactly the provided image")
        print("🚀 بدء التشغيل...")
        print("🚀 Starting application...")
        
        # إنشاء وتشغيل التطبيق
        app = WorkingReplicaInterface()
        app.run()
        
        print("👋 تم إغلاق البرنامج بنجاح")
        print("👋 Program closed successfully")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print(f"❌ Import error: {e}")
        print("\n📋 تأكد من تثبيت المكتبة المطلوبة:")
        print("📋 Make sure to install required library:")
        print("pip install customtkinter")
        input("اضغط Enter للخروج / Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        print(f"❌ Unexpected error: {e}")
        input("اضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
