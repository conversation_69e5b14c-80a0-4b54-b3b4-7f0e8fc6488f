#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل الواجهة المثالية مع دعم RTL
Run Perfect RTL Interface
"""

def main():
    """تشغيل الواجهة المثالية"""
    print("🎯" + "="*60)
    print("🎯 برنامج سيت الحل للمحاسبة - الواجهة المثالية")
    print("🎯 Set Al-Hal Accounting - Perfect Interface")
    print("🎯" + "="*60)
    print()
    print("📸 مطابقة تماماً للصورة المرفقة")
    print("📸 Exact match to the provided image")
    print("🔄 دعم الاتجاه من اليمين إلى اليسار (RTL)")
    print("🔄 Right-to-Left (RTL) support")
    print("✨ واجهة محسنة مع تأثيرات بصرية")
    print("✨ Enhanced interface with visual effects")
    print()
    print("🚀 بدء التشغيل...")
    print("🚀 Starting application...")
    print("="*70)
    
    try:
        from perfect_rtl_interface import PerfectRTLInterface
        
        print("✅ تم تحميل الواجهة المثالية بنجاح!")
        print("✅ Perfect interface loaded successfully!")
        
        # إنشاء وتشغيل التطبيق
        app = PerfectRTLInterface()
        app.run()
        
        print("="*70)
        print("👋 تم إغلاق البرنامج بنجاح")
        print("👋 Program closed successfully")
        print("🙏 شكراً لاستخدام برنامج سيت الحل للمحاسبة")
        print("🙏 Thank you for using Set Al-Hal Accounting")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print(f"❌ Import error: {e}")
        print("\n📋 تأكد من وجود الملفات المطلوبة:")
        print("📋 Make sure required files exist:")
        print("• perfect_rtl_interface.py")
        input("\nاضغط Enter للخروج / Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        print(f"❌ Unexpected error: {e}")
        input("\nاضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
