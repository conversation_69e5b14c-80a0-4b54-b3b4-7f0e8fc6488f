#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج المحاسبة المالية - الواجهة الرئيسية
Financial Accounting Program - Main Interface

تصميم واجهة رئيسية احترافية لبرنامج المحاسبة باللغة العربية
Professional main interface design for Arabic accounting software
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from datetime import datetime
import arabic_reshaper
from bidi.algorithm import get_display

# إعداد المظهر العام للتطبيق
ctk.set_appearance_mode("dark")  # الوضع الليلي
ctk.set_default_color_theme("blue")

class AccountingMainInterface:
    def __init__(self):
        """تهيئة الواجهة الرئيسية"""
        self.root = ctk.CTk()
        self.setup_window()
        self.create_menu_bar()
        self.create_top_bar()
        self.create_main_content()
        self.create_bottom_bar()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("برنامج سيت الحل للمحاسبة")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        
        # تعيين أيقونة النافذة (اختيارية)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
            
    def arabic_text(self, text):
        """تحويل النص العربي للعرض الصحيح"""
        reshaped_text = arabic_reshaper.reshape(text)
        return get_display(reshaped_text)
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم العلوي"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة ملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label=self.arabic_text("ملف"), menu=file_menu)
        file_menu.add_command(label=self.arabic_text("جديد"), command=self.new_file)
        file_menu.add_command(label=self.arabic_text("فتح"), command=self.open_file)
        file_menu.add_separator()
        file_menu.add_command(label=self.arabic_text("خروج"), command=self.root.quit)
        
        # قائمة تحرير
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label=self.arabic_text("تحرير"), menu=edit_menu)
        
        # قائمة تقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label=self.arabic_text("تقارير"), menu=reports_menu)
        
        # قائمة مساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label=self.arabic_text("مساعدة"), menu=help_menu)
        help_menu.add_command(label=self.arabic_text("حول البرنامج"), command=self.about)
        
    def create_top_bar(self):
        """إنشاء الشريط العلوي للمعلومات"""
        top_frame = ctk.CTkFrame(self.root, height=60, corner_radius=0)
        top_frame.pack(fill="x", padx=0, pady=0)
        top_frame.pack_propagate(False)
        
        # معلومات المستخدم والشركة
        info_frame = ctk.CTkFrame(top_frame, fg_color="transparent")
        info_frame.pack(side="right", fill="y", padx=10)
        
        user_label = ctk.CTkLabel(info_frame, text=self.arabic_text("المستخدم: أحمد محمد"), 
                                 font=ctk.CTkFont(size=12))
        user_label.pack(side="top", anchor="e")
        
        company_label = ctk.CTkLabel(info_frame, text=self.arabic_text("الشركة: شركة الحل للمحاسبة"), 
                                   font=ctk.CTkFont(size=12))
        company_label.pack(side="top", anchor="e")
        
        # شعار البرنامج
        logo_frame = ctk.CTkFrame(top_frame, fg_color="transparent")
        logo_frame.pack(side="left", fill="y", padx=10)
        
        logo_label = ctk.CTkLabel(logo_frame, text=self.arabic_text("برنامج سيت الحل للمحاسبة"), 
                                font=ctk.CTkFont(size=16, weight="bold"))
        logo_label.pack(side="top", anchor="w", pady=10)
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي مع الأيقونات"""
        # إطار المحتوى الرئيسي
        main_frame = ctk.CTkScrollableFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # عنوان الأقسام
        title_label = ctk.CTkLabel(main_frame, text=self.arabic_text("الأقسام الرئيسية"), 
                                 font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(pady=(0, 20))
        
        # تعريف الأقسام مع الألوان
        sections = [
            # الصف الأول
            [
                ("تقارير", "#2E8B57", self.open_reports),
                ("الفواتير", "#4169E1", self.open_invoices),
                ("الخزينة", "#20B2AA", self.open_treasury),
                ("الحسابات", "#FFD700", self.open_accounts),
                ("البضاعة", "#9370DB", self.open_inventory),
                ("تحليل البيانات", "#4682B4", self.open_analytics)
            ],
            # الصف الثاني
            [
                ("إدخال الحسابات", "#87CEEB", self.open_account_entry),
                ("إعداد", "#32CD32", self.open_settings),
                ("جرد", "#DC143C", self.open_inventory_count),
                ("صرف", "#FF6347", self.open_disbursement),
                ("مؤشرات", "#40E0D0", self.open_indicators)
            ],
            # الصف الثالث
            [
                ("مرتجع شراء", "#228B22", self.open_purchase_return),
                ("عرض أسعار", "#20B2AA", self.open_price_quote),
                ("مرتجع جرد", "#8A2BE2", self.open_inventory_return),
                ("قبض", "#9370DB", self.open_receipt),
                ("تحويل لمخزن", "#4169E1", self.open_warehouse_transfer),
                ("تسوية مخزن", "#32CD32", self.open_inventory_adjustment)
            ]
        ]
        
        # إنشاء الأيقونات
        for row_index, row in enumerate(sections):
            row_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
            row_frame.pack(fill="x", pady=10)
            
            for col_index, (name, color, command) in enumerate(row):
                self.create_section_button(row_frame, name, color, command, col_index, len(row))
                
    def create_section_button(self, parent, name, color, command, col_index, total_cols):
        """إنشاء زر قسم مع أيقونة"""
        # حساب العرض بناءً على عدد الأعمدة
        button_width = 180
        button_height = 120
        
        button_frame = ctk.CTkFrame(parent, fg_color="transparent")
        button_frame.pack(side="right" if col_index == 0 else "left", padx=10, pady=5)
        
        # الزر الرئيسي
        button = ctk.CTkButton(
            button_frame,
            text=self.arabic_text(name),
            width=button_width,
            height=button_height,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=color,
            hover_color=self.darken_color(color),
            corner_radius=15,
            command=command
        )
        button.pack()
        
    def darken_color(self, color):
        """تغميق اللون للحصول على تأثير hover"""
        # تحويل بسيط لتغميق اللون
        color_map = {
            "#2E8B57": "#1F5F3F",
            "#4169E1": "#2E4BC7",
            "#20B2AA": "#167B75",
            "#FFD700": "#CCB000",
            "#9370DB": "#7B5BC7",
            "#4682B4": "#2E5B8A",
            "#87CEEB": "#5BA3C7",
            "#32CD32": "#228B22",
            "#DC143C": "#A0102A",
            "#FF6347": "#CC4F39",
            "#40E0D0": "#2EB3A6",
            "#228B22": "#1A6B1A",
            "#8A2BE2": "#6B22B8",
        }
        return color_map.get(color, color)
        
    def create_bottom_bar(self):
        """إنشاء الشريط السفلي"""
        bottom_frame = ctk.CTkFrame(self.root, height=40, corner_radius=0)
        bottom_frame.pack(fill="x", side="bottom")
        bottom_frame.pack_propagate(False)

        # الوقت الحالي
        self.time_label = ctk.CTkLabel(bottom_frame, text=self.get_current_time(),
                                     font=ctk.CTkFont(size=12))
        self.time_label.pack(side="left", padx=10, pady=5)

        # معلومات إضافية
        status_label = ctk.CTkLabel(bottom_frame, text=self.arabic_text("متصل | الترخيص: نشط"),
                                  font=ctk.CTkFont(size=10))
        status_label.pack(side="left", padx=20, pady=5)

        # أزرار سريعة
        quick_buttons_frame = ctk.CTkFrame(bottom_frame, fg_color="transparent")
        quick_buttons_frame.pack(side="right", padx=10)

        # زر تبديل الوضع
        theme_btn = ctk.CTkButton(quick_buttons_frame, text="🌙",
                                width=40, height=30, command=self.toggle_theme)
        theme_btn.pack(side="right", padx=5)

        support_btn = ctk.CTkButton(quick_buttons_frame, text=self.arabic_text("خدمة العملاء"),
                                  width=100, height=30, command=self.customer_support)
        support_btn.pack(side="right", padx=5)

        subscription_btn = ctk.CTkButton(quick_buttons_frame, text=self.arabic_text("اشتراك"),
                                       width=80, height=30, command=self.subscription)
        subscription_btn.pack(side="right", padx=5)

        distributors_btn = ctk.CTkButton(quick_buttons_frame, text=self.arabic_text("الموزعين"),
                                       width=80, height=30, command=self.distributors)
        distributors_btn.pack(side="right", padx=5)
        
    def get_current_time(self):
        """الحصول على الوقت الحالي"""
        now = datetime.now()
        return now.strftime("%Y-%m-%d %H:%M:%S")
        
    # دوال الأقسام المختلفة
    def open_reports(self):
        """فتح قسم التقارير"""
        messagebox.showinfo("تقارير", self.arabic_text("سيتم فتح قسم التقارير"))
        
    def open_invoices(self):
        """فتح قسم الفواتير"""
        messagebox.showinfo("فواتير", self.arabic_text("سيتم فتح قسم الفواتير"))
        
    def open_treasury(self):
        """فتح قسم الخزينة"""
        messagebox.showinfo("خزينة", self.arabic_text("سيتم فتح قسم الخزينة"))
        
    def open_accounts(self):
        """فتح قسم الحسابات"""
        messagebox.showinfo("حسابات", self.arabic_text("سيتم فتح قسم الحسابات"))
        
    def open_inventory(self):
        """فتح قسم البضاعة"""
        messagebox.showinfo("بضاعة", self.arabic_text("سيتم فتح قسم البضاعة"))
        
    def open_analytics(self):
        """فتح قسم تحليل البيانات"""
        messagebox.showinfo("تحليل", self.arabic_text("سيتم فتح قسم تحليل البيانات"))
        
    def open_account_entry(self):
        """فتح إدخال الحسابات"""
        messagebox.showinfo("إدخال حسابات", self.arabic_text("سيتم فتح إدخال الحسابات"))
        
    def open_settings(self):
        """فتح الإعدادات"""
        messagebox.showinfo("إعدادات", self.arabic_text("سيتم فتح الإعدادات"))
        
    def open_inventory_count(self):
        """فتح جرد المخزن"""
        messagebox.showinfo("جرد", self.arabic_text("سيتم فتح جرد المخزن"))
        
    def open_disbursement(self):
        """فتح الصرف"""
        messagebox.showinfo("صرف", self.arabic_text("سيتم فتح قسم الصرف"))
        
    def open_indicators(self):
        """فتح المؤشرات"""
        messagebox.showinfo("مؤشرات", self.arabic_text("سيتم فتح المؤشرات"))
        
    def open_purchase_return(self):
        """فتح مرتجع الشراء"""
        messagebox.showinfo("مرتجع شراء", self.arabic_text("سيتم فتح مرتجع الشراء"))
        
    def open_price_quote(self):
        """فتح عرض الأسعار"""
        messagebox.showinfo("عرض أسعار", self.arabic_text("سيتم فتح عرض الأسعار"))
        
    def open_inventory_return(self):
        """فتح مرتجع الجرد"""
        messagebox.showinfo("مرتجع جرد", self.arabic_text("سيتم فتح مرتجع الجرد"))
        
    def open_receipt(self):
        """فتح القبض"""
        messagebox.showinfo("قبض", self.arabic_text("سيتم فتح قسم القبض"))
        
    def open_warehouse_transfer(self):
        """فتح تحويل المخزن"""
        messagebox.showinfo("تحويل مخزن", self.arabic_text("سيتم فتح تحويل المخزن"))
        
    def open_inventory_adjustment(self):
        """فتح تسوية المخزن"""
        messagebox.showinfo("تسوية مخزن", self.arabic_text("سيتم فتح تسوية المخزن"))
        
    # دوال القوائم
    def new_file(self):
        """ملف جديد"""
        messagebox.showinfo("ملف جديد", self.arabic_text("إنشاء ملف جديد"))
        
    def open_file(self):
        """فتح ملف"""
        messagebox.showinfo("فتح ملف", self.arabic_text("فتح ملف موجود"))
        
    def about(self):
        """حول البرنامج"""
        messagebox.showinfo("حول البرنامج", 
                          self.arabic_text("برنامج سيت الحل للمحاسبة\nإصدار 1.0\nتطوير: فريق التطوير"))
        
    def customer_support(self):
        """خدمة العملاء"""
        messagebox.showinfo("خدمة العملاء", self.arabic_text("الاتصال بخدمة العملاء"))
        
    def subscription(self):
        """الاشتراك"""
        messagebox.showinfo("اشتراك", self.arabic_text("معلومات الاشتراك"))
        
    def toggle_theme(self):
        """تبديل بين الوضع الليلي والنهاري"""
        current_mode = ctk.get_appearance_mode()
        if current_mode == "Dark":
            ctk.set_appearance_mode("light")
        else:
            ctk.set_appearance_mode("dark")

    def update_time(self):
        """تحديث الوقت كل ثانية"""
        if hasattr(self, 'time_label'):
            self.time_label.configure(text=self.get_current_time())
        self.root.after(1000, self.update_time)

    def run(self):
        """تشغيل التطبيق"""
        # بدء تحديث الوقت
        self.update_time()
        self.root.mainloop()

if __name__ == "__main__":
    # تشغيل التطبيق
    app = AccountingMainInterface()
    app.run()
