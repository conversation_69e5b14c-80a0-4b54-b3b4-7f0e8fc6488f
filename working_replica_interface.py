#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة مطابقة للصورة - نسخة عاملة
Working Replica Interface
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox

# إعداد المظهر
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class WorkingReplicaInterface:
    def __init__(self):
        """تهيئة الواجهة المطابقة للصورة"""
        self.root = ctk.CTk()
        self.setup_window()
        self.create_interface()
        
    def setup_window(self):
        """إعداد النافذة مطابقة للصورة"""
        self.root.title("برنامج سيت الحل للمحاسبة")
        self.root.geometry("1024x768")
        self.root.configure(fg_color="#2d5a3d")  # الخلفية الخضراء الداكنة
        
    def create_interface(self):
        """إنشاء الواجهة مطابقة للصورة تماماً"""
        
        # الشريط العلوي الأخضر
        top_frame = ctk.CTkFrame(self.root, height=120, corner_radius=0, 
                               fg_color="#4a7c59")
        top_frame.pack(fill="x", padx=0, pady=0)
        top_frame.pack_propagate(False)
        
        # شعار البرنامج
        logo_frame = ctk.CTkFrame(top_frame, width=300, height=100, 
                                corner_radius=10, fg_color="#2d5a3d")
        logo_frame.place(x=20, y=10)
        logo_frame.pack_propagate(False)
        
        # النص الرئيسي
        main_text = ctk.CTkLabel(logo_frame, 
                               text="برنامج سيت الحل",
                               font=ctk.CTkFont(size=20, weight="bold"),
                               text_color="white")
        main_text.place(x=150, y=25)
        
        sub_text = ctk.CTkLabel(logo_frame,
                              text="للمحاسبة",
                              font=ctk.CTkFont(size=16),
                              text_color="#90EE90")
        sub_text.place(x=150, y=55)
        
        # الأيقونات الصغيرة في الأعلى
        self.create_top_icons(top_frame)
        
        # المنطقة الرئيسية
        main_area = ctk.CTkFrame(self.root, fg_color="#2d5a3d")
        main_area.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان "تقارير"
        reports_label = ctk.CTkLabel(main_area, 
                                   text="تقارير",
                                   font=ctk.CTkFont(size=16, weight="bold"),
                                   text_color="white")
        reports_label.place(x=900, y=20)
        
        # شبكة الأيقونات الرئيسية
        self.create_main_icons_grid(main_area)
        
    def create_top_icons(self, parent):
        """الأيقونات الصغيرة في الأعلى"""
        top_icons = [
            ("مرتجعات", "#FF69B4", 450),
            ("مخزونية", "#32CD32", 550),
            ("محاسبية", "#4169E1", 650),
            ("الخزينة", "#FFD700", 750),
            ("المراجع", "#FF6347", 850),
            ("التقارير", "#9370DB", 950)
        ]
        
        for name, color, x_pos in top_icons:
            icon_frame = ctk.CTkFrame(parent, width=70, height=70, 
                                    corner_radius=8, fg_color=color,
                                    border_width=1, border_color="white")
            icon_frame.place(x=x_pos, y=25)
            icon_frame.pack_propagate(False)
            
            label = ctk.CTkLabel(icon_frame, 
                               text=name,
                               font=ctk.CTkFont(size=9, weight="bold"),
                               text_color="white",
                               wraplength=60)
            label.place(relx=0.5, rely=0.5, anchor="center")
            
    def create_main_icons_grid(self, parent):
        """شبكة الأيقونات الرئيسية"""
        
        # تعريف الأيقونات مطابقة للصورة
        icons_data = [
            # الصف الأول
            [
                ("إدخال الحسابات", "#87CEEB", 120, 60),
                ("إعداد", "#FFA500", 270, 60),
                ("شكل وتصنيف", "#20B2AA", 420, 60),
                ("إدخال الأصناف", "#FFD700", 570, 60),
                ("الحركة التوريدية", "#9370DB", 720, 60),
                ("تحليل البيانات", "#4682B4", 870, 60)
            ],
            # الصف الثاني
            [
                ("مخزن", "#FFA500", 120, 200),
                ("بيع", "#32CD32", 270, 200),
                ("قبض", "#DC143C", 420, 200),
                ("صرف", "#FF6347", 570, 200),
                ("مؤشرات", "#40E0D0", 720, 200)
            ],
            # الصف الثالث
            [
                ("مرتجع شراء", "#228B22", 120, 340),
                ("عرض أسعار", "#20B2AA", 270, 340),
                ("مرتجع جرد", "#8A2BE2", 420, 340),
                ("قبض", "#9370DB", 570, 340),
                ("تحويل لمخزن", "#4169E1", 720, 340),
                ("تسوية مخزن", "#32CD32", 870, 340)
            ]
        ]
        
        # إنشاء الأيقونات
        for row in icons_data:
            for name, color, x, y in row:
                self.create_main_icon(parent, name, color, x, y)
                
    def create_main_icon(self, parent, name, color, x, y):
        """إنشاء أيقونة رئيسية"""
        
        # إطار الأيقونة
        icon_frame = ctk.CTkFrame(parent, width=120, height=120, 
                                corner_radius=15, fg_color=color,
                                border_width=2, border_color="white")
        icon_frame.place(x=x, y=y)
        icon_frame.pack_propagate(False)
        
        # زر قابل للنقر
        button = ctk.CTkButton(icon_frame, text="", width=116, height=116,
                             fg_color="transparent", hover_color="transparent",
                             corner_radius=13,
                             command=lambda: self.icon_clicked(name))
        button.pack(fill="both", expand=True)
        
        # النص تحت الأيقونة
        text_label = ctk.CTkLabel(parent, 
                                text=name,
                                font=ctk.CTkFont(size=12, weight="bold"),
                                text_color="white")
        text_label.place(x=x+60, y=y+130, anchor="center")
        
    def icon_clicked(self, icon_name):
        """معالج النقر على الأيقونات"""
        messagebox.showinfo(
            f"قسم {icon_name}",
            f"تم النقر على قسم: {icon_name}\nسيتم فتح هذا القسم قريباً..."
        )
        
    def run(self):
        """تشغيل التطبيق"""
        print("🚀 تشغيل الواجهة المطابقة للصورة...")
        self.root.mainloop()
        print("✅ تم إغلاق البرنامج")

if __name__ == "__main__":
    try:
        app = WorkingReplicaInterface()
        app.run()
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للخروج...")
