#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المحاسبة مع دعم الاتجاه من اليمين إلى اليسار (RTL)
Accounting Interface with Right-to-Left (RTL) Support
"""

import tkinter as tk
from tkinter import messagebox
import sys

# محاولة استيراد مكتبات النص العربي
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False
    print("تحذير: مكتبات النص العربي غير متوفرة")

class RTLAccountingInterface:
    def __init__(self):
        """تهيئة الواجهة مع دعم RTL"""
        print("🚀 بدء تشغيل برنامج المحاسبة مع دعم RTL...")
        self.root = tk.Tk()
        self.setup_window()
        self.create_interface()
        print("✅ تم تحميل الواجهة بنجاح!")
        
    def setup_window(self):
        """إعداد النافذة مع دعم RTL"""
        self.root.title("برنامج سيت الحل للمحاسبة")
        self.root.geometry("1200x800")
        self.root.configure(bg="#2d5a3d")
        
        # جعل النافذة تظهر في المقدمة
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))
        
        # وضع النافذة في وسط الشاشة
        self.center_window()
        
    def center_window(self):
        """وضع النافذة في وسط الشاشة"""
        self.root.update_idletasks()
        width = 1200
        height = 800
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def arabic_text(self, text):
        """تحويل النص العربي للعرض الصحيح مع دعم RTL"""
        if ARABIC_SUPPORT:
            try:
                reshaped_text = arabic_reshaper.reshape(text)
                return get_display(reshaped_text)
            except:
                return text
        return text
        
    def create_interface(self):
        """إنشاء الواجهة مطابقة للصورة مع دعم RTL"""
        
        # الشريط العلوي الأخضر
        top_frame = tk.Frame(self.root, height=100, bg="#4a7c59")
        top_frame.pack(fill="x", padx=0, pady=0)
        top_frame.pack_propagate(False)
        
        # شعار البرنامج في الأعلى اليسار (كما في الصورة)
        logo_frame = tk.Frame(top_frame, width=350, height=80, bg="#2d5a3d", relief="raised", bd=2)
        logo_frame.place(x=20, y=10)
        logo_frame.pack_propagate(False)
        
        # النص الرئيسي للشعار
        main_text = tk.Label(logo_frame, 
                           text=self.arabic_text("برنامج سيت الحل"),
                           font=("Arial", 18, "bold"),
                           bg="#2d5a3d",
                           fg="white",
                           anchor="e")  # محاذاة لاليمين
        main_text.place(x=250, y=20)
        
        sub_text = tk.Label(logo_frame,
                          text=self.arabic_text("للمحاسبة"),
                          font=("Arial", 14),
                          bg="#2d5a3d",
                          fg="#90EE90",
                          anchor="e")  # محاذاة لليمين
        sub_text.place(x=250, y=50)
        
        # معلومات إضافية في الأعلى اليمين
        info_frame = tk.Frame(top_frame, bg="#4a7c59")
        info_frame.place(x=900, y=20)
        
        user_label = tk.Label(info_frame,
                            text=self.arabic_text("المستخدم: أحمد محمد"),
                            font=("Arial", 10),
                            bg="#4a7c59",
                            fg="white",
                            anchor="e")
        user_label.pack(anchor="e")
        
        company_label = tk.Label(info_frame,
                               text=self.arabic_text("الشركة: شركة التجارة"),
                               font=("Arial", 10),
                               bg="#4a7c59",
                               fg="#90EE90",
                               anchor="e")
        company_label.pack(anchor="e")
        
        # المنطقة الرئيسية للأيقونات
        main_area = tk.Frame(self.root, bg="#2d5a3d")
        main_area.pack(fill="both", expand=True, padx=30, pady=20)
        
        # عنوان "تقارير" في الأعلى اليمين (كما في الصورة)
        reports_label = tk.Label(main_area, 
                               text=self.arabic_text("تقارير"),
                               font=("Arial", 14, "bold"),
                               bg="#2d5a3d",
                               fg="white",
                               anchor="e")
        reports_label.place(x=1050, y=10)
        
        # إنشاء شبكة الأيقونات مع ترتيب RTL
        self.create_rtl_icons_grid(main_area)
        
        # شريط الحالة السفلي
        status_frame = tk.Frame(self.root, height=35, bg="#4a7c59")
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)
        
        status_label = tk.Label(status_frame,
                              text=self.arabic_text("✅ البرنامج جاهز | 🖱️ انقر على أي أيقونة"),
                              font=("Arial", 11),
                              bg="#4a7c59",
                              fg="white")
        status_label.pack(pady=8)
        
    def create_rtl_icons_grid(self, parent):
        """إنشاء شبكة الأيقونات مع ترتيب RTL مطابق للصورة"""
        
        # تعريف الأيقونات بترتيب RTL مطابق للصورة تماماً
        icons_data = [
            # الصف الأول (6 أيقونات) - من اليمين لليسار
            [
                (self.arabic_text("تحليل البيانات"), "#4682B4"),
                (self.arabic_text("البضاعة"), "#9370DB"),
                (self.arabic_text("الحسابات"), "#FFD700"),
                (self.arabic_text("الخزينة"), "#20B2AA"),
                (self.arabic_text("الفواتير"), "#FFA500"),
                (self.arabic_text("تقارير"), "#87CEEB")
            ],
            # الصف الثاني (5 أيقونات) - من اليمين لليسار
            [
                (self.arabic_text("مؤشرات"), "#40E0D0"),
                (self.arabic_text("صرف"), "#FF6347"),
                (self.arabic_text("قبض"), "#DC143C"),
                (self.arabic_text("بيع"), "#32CD32"),
                (self.arabic_text("مخزن"), "#FFA500")
            ],
            # الصف الثالث (6 أيقونات) - من اليمين لليسار
            [
                (self.arabic_text("تسوية مخزن"), "#32CD32"),
                (self.arabic_text("تحويل لمخزن"), "#4169E1"),
                (self.arabic_text("قبض"), "#9370DB"),
                (self.arabic_text("مرتجع جرد"), "#8A2BE2"),
                (self.arabic_text("عرض أسعار"), "#20B2AA"),
                (self.arabic_text("مرتجع شراء"), "#228B22")
            ]
        ]
        
        # إنشاء الشبكة مع ترتيب RTL
        start_x = 950  # البداية من اليمين
        start_y = 80
        icon_width = 150
        icon_height = 120
        spacing_x = 170
        spacing_y = 160
        
        for row_idx, row in enumerate(icons_data):
            for col_idx, (name, color) in enumerate(row):
                # حساب الموضع من اليمين لليسار
                x = start_x - (col_idx * spacing_x)
                y = start_y + (row_idx * spacing_y)
                
                self.create_rtl_icon(parent, name, color, x, y, icon_width, icon_height)
                
    def create_rtl_icon(self, parent, name, color, x, y, width, height):
        """إنشاء أيقونة مع دعم RTL"""
        
        # إطار الأيقونة
        icon_frame = tk.Frame(parent, width=width, height=height, 
                            bg=color, relief="raised", bd=3)
        icon_frame.place(x=x, y=y)
        icon_frame.pack_propagate(False)
        
        # زر قابل للنقر
        button = tk.Button(icon_frame, 
                         text="📊",
                         font=("Arial", 28),
                         bg=color,
                         fg="white",
                         relief="flat",
                         activebackground=self.darken_color(color),
                         command=lambda: self.icon_clicked(name))
        button.pack(fill="both", expand=True)
        
        # النص تحت الأيقونة مع محاذاة RTL
        text_label = tk.Label(parent, 
                            text=name,
                            font=("Arial", 11, "bold"),
                            bg="#2d5a3d",
                            fg="white",
                            anchor="center")
        text_label.place(x=x + width//2, y=y + height + 5, anchor="center")
        
    def darken_color(self, color):
        """تغميق اللون للتأثير التفاعلي"""
        color_map = {
            "#87CEEB": "#5F9EA0",
            "#FFA500": "#FF8C00",
            "#20B2AA": "#008B8B",
            "#FFD700": "#DAA520",
            "#9370DB": "#8A2BE2",
            "#4682B4": "#4169E1",
            "#228B22": "#006400",
            "#32CD32": "#228B22",
            "#DC143C": "#B22222",
            "#FF6347": "#CD5C5C",
            "#40E0D0": "#20B2AA",
            "#8A2BE2": "#7B68EE",
            "#FF69B4": "#FF1493"
        }
        return color_map.get(color, color)
        
    def icon_clicked(self, icon_name):
        """معالج النقر على الأيقونات"""
        # إزالة التشكيل للعرض في الرسالة
        clean_name = icon_name.replace("ً", "").replace("ٌ", "").replace("ٍ", "")
        
        messagebox.showinfo(
            f"قسم {clean_name}",
            f"🎯 تم فتح قسم: {clean_name}\n\n"
            f"📋 الوظائف المتاحة:\n"
            f"• عرض البيانات\n"
            f"• إضافة جديد\n"
            f"• تعديل الموجود\n"
            f"• طباعة التقارير\n\n"
            f"✨ قيد التطوير..."
        )
        
    def run(self):
        """تشغيل التطبيق"""
        print("🖥️ عرض النافذة مع دعم RTL...")
        print("📱 الواجهة مطابقة للصورة مع ترتيب من اليمين لليسار!")
        self.root.mainloop()
        print("👋 تم إغلاق البرنامج")

if __name__ == "__main__":
    try:
        app = RTLAccountingInterface()
        app.run()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
